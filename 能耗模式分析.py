#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
电力系统能耗模式分析与聚类
作者：数据科学与大数据技术专业学生
日期：2024年
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class EnergyConsumptionAnalysis:
    """能耗模式分析类"""
    
    def __init__(self, data):
        self.data = data
        self.scaler = StandardScaler()
        self.kmeans = None
        self.pca = None
        
    def prepare_clustering_features(self):
        """准备聚类特征"""
        # 计算每日统计特征
        daily_stats = self.data.groupby(self.data.index.date).agg({
            'load': ['mean', 'max', 'min', 'std'],
            'temperature': ['mean', 'max', 'min'],
            'humidity': 'mean',
            'wind_speed': 'mean'
        }).round(2)
        
        # 扁平化列名
        daily_stats.columns = ['_'.join(col).strip() for col in daily_stats.columns]
        
        # 添加时间特征
        daily_stats['month'] = pd.to_datetime(daily_stats.index).month
        daily_stats['day_of_week'] = pd.to_datetime(daily_stats.index).dayofweek
        daily_stats['is_weekend'] = (daily_stats['day_of_week'] >= 5).astype(int)
        
        # 计算负荷变化率
        daily_stats['load_range'] = daily_stats['load_max'] - daily_stats['load_min']
        daily_stats['load_cv'] = daily_stats['load_std'] / daily_stats['load_mean']  # 变异系数
        
        return daily_stats
    
    def perform_clustering(self, n_clusters=4):
        """执行K-means聚类分析"""
        print("开始能耗模式聚类分析...")
        
        # 准备聚类特征
        clustering_data = self.prepare_clustering_features()
        
        # 选择聚类特征
        feature_cols = ['load_mean', 'load_max', 'load_min', 'load_std', 'load_range', 'load_cv',
                       'temperature_mean', 'humidity_mean', 'month', 'day_of_week', 'is_weekend']
        
        X = clustering_data[feature_cols]
        
        # 标准化
        X_scaled = self.scaler.fit_transform(X)
        
        # K-means聚类
        self.kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        clusters = self.kmeans.fit_predict(X_scaled)
        
        # 添加聚类结果
        clustering_data['cluster'] = clusters
        
        # PCA降维用于可视化
        self.pca = PCA(n_components=2)
        X_pca = self.pca.fit_transform(X_scaled)
        clustering_data['pca1'] = X_pca[:, 0]
        clustering_data['pca2'] = X_pca[:, 1]
        
        self.clustering_data = clustering_data
        
        # 分析聚类结果
        self.analyze_clusters()
        
        return clustering_data
    
    def analyze_clusters(self):
        """分析聚类结果"""
        print("\n聚类结果分析：")
        
        # 计算每个聚类的统计信息
        cluster_stats = self.clustering_data.groupby('cluster').agg({
            'load_mean': ['mean', 'std'],
            'load_max': ['mean', 'std'],
            'temperature_mean': ['mean', 'std'],
            'is_weekend': 'mean',
            'month': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else x.mean()
        }).round(2)
        
        print("各聚类统计信息：")
        print(cluster_stats)
        
        # 聚类标签解释
        cluster_labels = {
            0: "低负荷稳定型",
            1: "高负荷波动型", 
            2: "中等负荷规律型",
            3: "季节性负荷型"
        }
        
        print("\n聚类模式解释：")
        for i in range(len(cluster_labels)):
            count = (self.clustering_data['cluster'] == i).sum()
            percentage = count / len(self.clustering_data) * 100
            print(f"聚类{i} ({cluster_labels[i]}): {count}天 ({percentage:.1f}%)")
    
    def visualize_clustering_results(self):
        """可视化聚类结果"""
        print("生成聚类分析图表...")
        
        # 图表9：能耗模式聚类结果图
        plt.figure(figsize=(15, 10))
        
        # PCA可视化
        plt.subplot(2, 2, 1)
        scatter = plt.scatter(self.clustering_data['pca1'], 
                            self.clustering_data['pca2'],
                            c=self.clustering_data['cluster'], 
                            cmap='viridis', alpha=0.7)
        plt.colorbar(scatter)
        plt.title('能耗模式聚类结果（PCA降维）')
        plt.xlabel(f'第一主成分 (解释方差: {self.pca.explained_variance_ratio_[0]:.2%})')
        plt.ylabel(f'第二主成分 (解释方差: {self.pca.explained_variance_ratio_[1]:.2%})')
        
        # 负荷均值vs最大值
        plt.subplot(2, 2, 2)
        scatter = plt.scatter(self.clustering_data['load_mean'], 
                            self.clustering_data['load_max'],
                            c=self.clustering_data['cluster'], 
                            cmap='viridis', alpha=0.7)
        plt.colorbar(scatter)
        plt.title('负荷均值 vs 最大值聚类')
        plt.xlabel('日均负荷 (MW)')
        plt.ylabel('日最大负荷 (MW)')
        
        # 温度vs负荷
        plt.subplot(2, 2, 3)
        scatter = plt.scatter(self.clustering_data['temperature_mean'], 
                            self.clustering_data['load_mean'],
                            c=self.clustering_data['cluster'], 
                            cmap='viridis', alpha=0.7)
        plt.colorbar(scatter)
        plt.title('温度 vs 负荷聚类')
        plt.xlabel('日均温度 (°C)')
        plt.ylabel('日均负荷 (MW)')
        
        # 聚类分布
        plt.subplot(2, 2, 4)
        cluster_counts = self.clustering_data['cluster'].value_counts().sort_index()
        plt.bar(range(len(cluster_counts)), cluster_counts.values, 
                color=['red', 'blue', 'green', 'orange'])
        plt.title('各聚类样本数量分布')
        plt.xlabel('聚类编号')
        plt.ylabel('样本数量')
        plt.xticks(range(len(cluster_counts)), 
                  [f'聚类{i}' for i in range(len(cluster_counts))])
        
        plt.tight_layout()
        plt.savefig('图表9_能耗模式聚类结果.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def seasonal_analysis(self):
        """季节性能耗分析"""
        print("进行季节性能耗分析...")
        
        # 按季节分组
        self.data['season'] = self.data.index.month.map({
            12: '冬季', 1: '冬季', 2: '冬季',
            3: '春季', 4: '春季', 5: '春季',
            6: '夏季', 7: '夏季', 8: '夏季',
            9: '秋季', 10: '秋季', 11: '秋季'
        })
        
        # 图表10：不同季节的能耗模式对比图
        plt.figure(figsize=(15, 10))
        
        # 季节性负荷分布
        plt.subplot(2, 2, 1)
        seasons = ['春季', '夏季', '秋季', '冬季']
        season_data = [self.data[self.data['season'] == season]['load'] for season in seasons]
        plt.boxplot(season_data, labels=seasons)
        plt.title('不同季节负荷分布')
        plt.ylabel('负荷 (MW)')
        plt.grid(True, alpha=0.3)
        
        # 季节性日负荷曲线
        plt.subplot(2, 2, 2)
        for season in seasons:
            season_hourly = self.data[self.data['season'] == season].groupby('hour')['load'].mean()
            plt.plot(season_hourly.index, season_hourly.values, 
                    marker='o', label=season, linewidth=2)
        plt.title('不同季节典型日负荷曲线')
        plt.xlabel('小时')
        plt.ylabel('平均负荷 (MW)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 季节性温度vs负荷关系
        plt.subplot(2, 2, 3)
        for season in seasons:
            season_data = self.data[self.data['season'] == season]
            plt.scatter(season_data['temperature'], season_data['load'], 
                       alpha=0.5, label=season, s=1)
        plt.title('不同季节温度与负荷关系')
        plt.xlabel('温度 (°C)')
        plt.ylabel('负荷 (MW)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 月度能耗统计
        plt.subplot(2, 2, 4)
        monthly_consumption = self.data.groupby('month')['load'].sum()
        plt.bar(monthly_consumption.index, monthly_consumption.values, 
                color='skyblue', edgecolor='navy')
        plt.title('月度总能耗')
        plt.xlabel('月份')
        plt.ylabel('总负荷 (MW·h)')
        plt.xticks(range(1, 13))
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('图表10_季节性能耗模式对比.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def feature_importance_analysis(self):
        """特征重要性分析"""
        print("进行特征重要性分析...")
        
        # 准备特征和目标变量
        feature_cols = ['temperature', 'humidity', 'wind_speed', 'precipitation',
                       'hour', 'day_of_week', 'month', 'is_weekend']
        
        X = self.data[feature_cols]
        y = self.data['load']
        
        # 使用随机森林分析特征重要性
        rf = RandomForestRegressor(n_estimators=100, random_state=42)
        rf.fit(X, y)
        
        # 获取特征重要性
        importance = rf.feature_importances_
        feature_importance = pd.DataFrame({
            'feature': feature_cols,
            'importance': importance
        }).sort_values('importance', ascending=False)
        
        # 图表11：特征重要性分析图
        plt.figure(figsize=(12, 8))
        
        plt.subplot(2, 1, 1)
        plt.barh(range(len(feature_importance)), feature_importance['importance'])
        plt.yticks(range(len(feature_importance)), feature_importance['feature'])
        plt.xlabel('重要性得分')
        plt.title('负荷预测特征重要性分析')
        plt.grid(True, alpha=0.3)
        
        # 特征重要性饼图
        plt.subplot(2, 1, 2)
        plt.pie(feature_importance['importance'], labels=feature_importance['feature'], 
                autopct='%1.1f%%', startangle=90)
        plt.title('特征重要性占比')
        
        plt.tight_layout()
        plt.savefig('图表11_特征重要性分析.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("特征重要性排序：")
        for i, row in feature_importance.iterrows():
            print(f"{row['feature']}: {row['importance']:.4f}")
        
        return feature_importance
    
    def generate_summary_report(self):
        """生成分析总结报告"""
        print("\n=== 能耗分析总结报告 ===")
        
        # 基本统计
        total_consumption = self.data['load'].sum()
        avg_load = self.data['load'].mean()
        peak_load = self.data['load'].max()
        min_load = self.data['load'].min()
        
        print(f"总能耗: {total_consumption:,.0f} MW·h")
        print(f"平均负荷: {avg_load:.2f} MW")
        print(f"峰值负荷: {peak_load:.2f} MW")
        print(f"最低负荷: {min_load:.2f} MW")
        print(f"负荷变化范围: {peak_load - min_load:.2f} MW")
        
        # 季节性分析
        seasonal_avg = self.data.groupby('season')['load'].mean()
        print(f"\n季节性负荷特征:")
        for season, avg in seasonal_avg.items():
            print(f"{season}: {avg:.2f} MW")
        
        # 工作日vs周末
        weekday_avg = self.data[self.data['is_weekend'] == 0]['load'].mean()
        weekend_avg = self.data[self.data['is_weekend'] == 1]['load'].mean()
        print(f"\n工作日平均负荷: {weekday_avg:.2f} MW")
        print(f"周末平均负荷: {weekend_avg:.2f} MW")
        print(f"工作日/周末差异: {((weekday_avg - weekend_avg) / weekend_avg * 100):+.1f}%")

def main():
    """主函数"""
    print("=== 电力系统能耗模式分析 ===")
    
    # 这里需要先运行主分析脚本获取数据
    print("请先运行 '电力负荷预测分析.py' 生成数据")
    print("然后将数据传入此分析模块")
    
    # 示例：如果有数据文件
    # data = pd.read_csv('power_load_data.csv', index_col='datetime', parse_dates=True)
    # analyzer = EnergyConsumptionAnalysis(data)
    # analyzer.perform_clustering()
    # analyzer.visualize_clustering_results()
    # analyzer.seasonal_analysis()
    # analyzer.feature_importance_analysis()
    # analyzer.generate_summary_report()

if __name__ == "__main__":
    main()
