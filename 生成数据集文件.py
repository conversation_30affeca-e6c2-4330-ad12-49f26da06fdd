#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成论文使用的电力负荷数据集
用于提交作业时的数据集文件
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

def generate_power_load_dataset():
    """生成完整的电力负荷数据集"""
    print("正在生成电力负荷数据集...")
    
    # 设置随机种子确保可重现
    np.random.seed(42)
    
    # 生成更大的数据集用于提交
    n_samples = 8760  # 一年的小时数据
    dates = pd.date_range('2023-01-01', periods=n_samples, freq='H')
    
    print(f"生成时间范围: {dates[0]} 到 {dates[-1]}")
    print(f"数据点数量: {n_samples}")
    
    # 基础负荷模式 (MW)
    base_load = 1000
    
    # 1. 季节性变化 (年周期)
    seasonal_yearly = 200 * np.sin(2 * np.pi * np.arange(n_samples) / (365.25 * 24))
    
    # 2. 周周期变化
    weekly_pattern = 150 * np.sin(2 * np.pi * np.arange(n_samples) / (7 * 24))
    
    # 3. 日周期变化
    daily_pattern = 300 * np.sin(2 * np.pi * np.arange(n_samples) / 24 - np.pi/2)
    
    # 4. 工作日/周末差异
    is_weekend = pd.Series(dates).dt.dayofweek >= 5
    weekend_effect = np.where(is_weekend, -100, 50)
    
    # 5. 气象数据生成
    # 温度 (°C) - 具有季节性和日变化
    temperature = (15 + 
                  10 * np.sin(2 * np.pi * np.arange(n_samples) / (365.25 * 24)) +  # 季节性
                  5 * np.sin(2 * np.pi * np.arange(n_samples) / 24) +              # 日变化
                  np.random.normal(0, 3, n_samples))                               # 随机波动
    
    # 湿度 (%) - 季节性变化
    humidity = (60 + 
               20 * np.sin(2 * np.pi * np.arange(n_samples) / (365.25 * 24) + np.pi/4) +
               np.random.normal(0, 10, n_samples))
    humidity = np.clip(humidity, 20, 95)  # 限制在合理范围
    
    # 风速 (m/s) - 指数分布
    wind_speed = 3 + np.random.exponential(2, n_samples)
    wind_speed = np.clip(wind_speed, 0, 15)  # 限制最大风速
    
    # 降水量 (mm) - 指数分布
    precipitation = np.random.exponential(0.5, n_samples)
    
    # 6. 温度对负荷的影响
    # 制冷负荷 (温度>25°C) 和供暖负荷 (温度<5°C)
    temp_effect = np.where(temperature > 25, (temperature - 25) * 20,  # 制冷
                          np.where(temperature < 5, (5 - temperature) * 15, 0))  # 供暖
    
    # 7. 湿度影响 (高湿度增加制冷负荷)
    humidity_effect = np.where(humidity > 70, (humidity - 70) * 2, 0)
    
    # 8. 随机噪声
    noise = np.random.normal(0, 50, n_samples)
    
    # 9. 合成最终负荷
    load = (base_load + seasonal_yearly + weekly_pattern + daily_pattern + 
            weekend_effect + temp_effect + humidity_effect + noise)
    
    # 确保负荷为正值
    load = np.maximum(load, 100)
    
    # 10. 创建完整数据框
    data = pd.DataFrame({
        'datetime': dates,
        'load': load.round(2),  # 负荷 (MW)
        'temperature': temperature.round(1),  # 温度 (°C)
        'humidity': humidity.round(1),  # 湿度 (%)
        'wind_speed': wind_speed.round(1),  # 风速 (m/s)
        'precipitation': precipitation.round(2),  # 降水量 (mm)
        'hour': dates.hour,  # 小时 (0-23)
        'day_of_week': dates.dayofweek,  # 星期几 (0-6, 0=周一)
        'month': dates.month,  # 月份 (1-12)
        'quarter': dates.quarter,  # 季度 (1-4)
        'is_weekend': is_weekend.astype(int),  # 是否周末 (0/1)
        'is_holiday': np.random.choice([0, 1], n_samples, p=[0.95, 0.05]),  # 是否节假日
        'day_of_year': dates.dayofyear,  # 一年中的第几天
        'week_of_year': dates.isocalendar().week.values,  # 一年中的第几周
    })
    
    # 设置datetime为索引
    data.set_index('datetime', inplace=True)

    # 添加季节标签
    data['season'] = data['month'].map({
        12: '冬季', 1: '冬季', 2: '冬季',
        3: '春季', 4: '春季', 5: '春季',
        6: '夏季', 7: '夏季', 8: '夏季',
        9: '秋季', 10: '秋季', 11: '秋季'
    })

    # 添加时段标签
    data['time_period'] = data['hour'].map(lambda x:
        '深夜' if 0 <= x < 6 else
        '早晨' if 6 <= x < 12 else
        '下午' if 12 <= x < 18 else
        '晚上')

    # 添加工作日类型
    data['day_type'] = data.apply(lambda row:
        '节假日' if row['is_holiday'] == 1 else
        '周末' if row['is_weekend'] == 1 else
        '工作日', axis=1)

    # 重置索引，将datetime作为普通列
    data.reset_index(inplace=True)
    
    print(f"数据集生成完成！")
    print(f"数据集大小: {data.shape}")
    print(f"时间跨度: {data['datetime'].min()} 到 {data['datetime'].max()}")
    
    return data

def save_dataset_files(data):
    """保存数据集为多种格式"""
    print("\n正在保存数据集文件...")
    
    # 1. 保存为CSV格式 (最常用)
    data.to_csv('电力负荷数据集.csv', index=False, encoding='utf-8-sig')
    print("✓ 已保存: 电力负荷数据集.csv")
    
    # 2. 保存为Excel格式 (便于查看)
    with pd.ExcelWriter('电力负荷数据集.xlsx', engine='openpyxl') as writer:
        # 完整数据
        data.to_excel(writer, sheet_name='完整数据', index=False)
        
        # 数据说明
        description = pd.DataFrame({
            '字段名': ['datetime', 'load', 'temperature', 'humidity', 'wind_speed', 
                     'precipitation', 'hour', 'day_of_week', 'month', 'quarter',
                     'is_weekend', 'is_holiday', 'day_of_year', 'week_of_year',
                     'season', 'time_period', 'day_type'],
            '数据类型': ['日期时间', '数值', '数值', '数值', '数值', '数值', 
                      '整数', '整数', '整数', '整数', '二进制', '二进制',
                      '整数', '整数', '分类', '分类', '分类'],
            '单位': ['年-月-日 时:分:秒', 'MW', '°C', '%', 'm/s', 'mm',
                   '-', '-', '-', '-', '0/1', '0/1', '-', '-', '-', '-', '-'],
            '说明': ['时间戳', '电力负荷', '环境温度', '相对湿度', '风速', '降水量',
                   '小时(0-23)', '星期几(0-6)', '月份(1-12)', '季度(1-4)',
                   '是否周末', '是否节假日', '一年中第几天', '一年中第几周',
                   '季节', '时段', '日期类型']
        })
        description.to_excel(writer, sheet_name='字段说明', index=False)
        
        # 基本统计
        stats = data.select_dtypes(include=[np.number]).describe()
        stats.to_excel(writer, sheet_name='基本统计')
        
    print("✓ 已保存: 电力负荷数据集.xlsx")
    
    # 3. 保存训练集和测试集
    split_point = int(len(data) * 0.8)
    train_data = data[:split_point]
    test_data = data[split_point:]
    
    train_data.to_csv('训练集.csv', index=False, encoding='utf-8-sig')
    test_data.to_csv('测试集.csv', index=False, encoding='utf-8-sig')
    print("✓ 已保存: 训练集.csv")
    print("✓ 已保存: 测试集.csv")
    
    # 4. 保存数据样本 (前100行，便于快速查看)
    sample_data = data.head(100)
    sample_data.to_csv('数据样本.csv', index=False, encoding='utf-8-sig')
    print("✓ 已保存: 数据样本.csv")

def generate_data_documentation():
    """生成数据集文档"""
    print("\n正在生成数据集文档...")
    
    doc_content = """# 电力负荷数据集文档

## 数据集概述
- **数据集名称**: 电力负荷预测数据集
- **数据类型**: 时间序列数据
- **数据规模**: 8760条记录 (一年的小时数据)
- **时间范围**: 2023年1月1日 00:00 - 2023年12月31日 23:00
- **采样频率**: 每小时
- **文件格式**: CSV, Excel
- **编码格式**: UTF-8

## 字段说明

### 主要变量
1. **datetime** (日期时间): 时间戳，格式为 YYYY-MM-DD HH:MM:SS
2. **load** (数值): 电力负荷，单位为兆瓦(MW)，范围约600-1800MW

### 气象变量
3. **temperature** (数值): 环境温度，单位为摄氏度(°C)
4. **humidity** (数值): 相对湿度，单位为百分比(%)，范围20-95%
5. **wind_speed** (数值): 风速，单位为米/秒(m/s)，范围0-15m/s
6. **precipitation** (数值): 降水量，单位为毫米(mm)

### 时间特征
7. **hour** (整数): 小时，范围0-23
8. **day_of_week** (整数): 星期几，0=周一，6=周日
9. **month** (整数): 月份，范围1-12
10. **quarter** (整数): 季度，范围1-4
11. **day_of_year** (整数): 一年中的第几天，范围1-365
12. **week_of_year** (整数): 一年中的第几周，范围1-53

### 分类特征
13. **is_weekend** (二进制): 是否周末，0=工作日，1=周末
14. **is_holiday** (二进制): 是否节假日，0=非节假日，1=节假日
15. **season** (分类): 季节，春季/夏季/秋季/冬季
16. **time_period** (分类): 时段，深夜/早晨/下午/晚上
17. **day_type** (分类): 日期类型，工作日/周末/节假日

## 数据特征

### 负荷特征
- 平均负荷: 约1000MW
- 峰值负荷: 约1800MW
- 最低负荷: 约600MW
- 标准差: 约200MW

### 时间模式
- **日周期**: 明显的24小时周期性，白天负荷高，夜间负荷低
- **周周期**: 工作日负荷高于周末
- **季节性**: 夏季和冬季负荷较高（制冷和供暖需求）

### 气象影响
- **温度效应**: 极端温度（>25°C或<5°C）显著影响负荷
- **湿度效应**: 高湿度增加制冷负荷
- **其他气象因素**: 风速和降水量影响相对较小

## 使用说明

### 数据加载
```python
import pandas as pd
data = pd.read_csv('电力负荷数据集.csv', parse_dates=['datetime'])
```

### 数据分割
- 训练集: 前80%数据 (约7000条记录)
- 测试集: 后20%数据 (约1760条记录)

### 适用任务
1. 时间序列预测
2. 回归分析
3. 聚类分析
4. 特征重要性分析
5. 季节性分解

## 数据质量
- **完整性**: 无缺失值
- **一致性**: 时间序列连续，无重复
- **准确性**: 数值范围合理，符合实际情况
- **时效性**: 涵盖完整年度周期

## 注意事项
1. 本数据集为模拟生成，用于教学和研究目的
2. 数据特征基于真实电力系统规律设计
3. 使用时请注明数据来源和生成方法
4. 建议结合领域知识进行特征工程

## 版本信息
- 版本: 1.0
- 生成日期: 2024年
- 生成工具: Python + pandas + numpy
- 随机种子: 42 (确保可重现)
"""
    
    with open('数据集文档.md', 'w', encoding='utf-8') as f:
        f.write(doc_content)
    
    print("✓ 已保存: 数据集文档.md")

def print_dataset_summary(data):
    """打印数据集摘要信息"""
    print("\n" + "="*50)
    print("数据集摘要信息")
    print("="*50)
    
    print(f"数据集大小: {data.shape[0]} 行 × {data.shape[1]} 列")
    print(f"时间范围: {data['datetime'].min()} 到 {data['datetime'].max()}")
    print(f"数据类型: {data.dtypes.value_counts().to_dict()}")
    
    print("\n数值变量统计:")
    numeric_cols = ['load', 'temperature', 'humidity', 'wind_speed', 'precipitation']
    for col in numeric_cols:
        print(f"{col}: 均值={data[col].mean():.2f}, 标准差={data[col].std():.2f}, "
              f"范围=[{data[col].min():.2f}, {data[col].max():.2f}]")
    
    print("\n分类变量分布:")
    print(f"季节分布: {data['season'].value_counts().to_dict()}")
    print(f"日期类型: {data['day_type'].value_counts().to_dict()}")
    print(f"时段分布: {data['time_period'].value_counts().to_dict()}")
    
    print("\n前5行数据预览:")
    print(data.head())

def main():
    """主函数"""
    print("="*60)
    print("电力负荷数据集生成器")
    print("用于《数据仓库技术与应用》课程作业提交")
    print("="*60)
    
    # 1. 生成数据集
    data = generate_power_load_dataset()
    
    # 2. 保存数据集文件
    save_dataset_files(data)
    
    # 3. 生成文档
    generate_data_documentation()
    
    # 4. 打印摘要
    print_dataset_summary(data)
    
    print("\n" + "="*60)
    print("数据集生成完成！")
    print("="*60)
    print("生成的文件:")
    print("📊 电力负荷数据集.csv - 完整数据集(CSV格式)")
    print("📊 电力负荷数据集.xlsx - 完整数据集(Excel格式，包含多个工作表)")
    print("📊 训练集.csv - 训练数据(前80%)")
    print("📊 测试集.csv - 测试数据(后20%)")
    print("📊 数据样本.csv - 数据样本(前100行)")
    print("📋 数据集文档.md - 详细的数据集说明文档")
    print("\n这些文件可以直接用于作业提交！")

if __name__ == "__main__":
    main()
