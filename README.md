# 电力系统负荷预测与能耗分析

## 项目简介

本项目是山东协和学院数据科学与大数据技术专业《数据仓库技术与应用》课程的期末作业，选题为"电力系统负荷预测与能耗分析"。

## 文件结构

```
├── 电力系统负荷预测与能耗分析论文.md    # 完整论文
├── 电力负荷预测分析.py                  # 主要分析代码
├── 能耗模式分析.py                      # 能耗模式分析代码
├── 完整分析运行脚本.py                  # 整合所有分析的运行脚本
├── requirements.txt                     # Python依赖包
├── README.md                           # 说明文档
└── 期末作业要求.md                     # 作业要求文档
```

## 环境要求

- Python 3.8+
- 推荐使用Anaconda或Miniconda

## 安装依赖

```bash
pip install -r requirements.txt
```

或者使用conda：

```bash
conda install pandas numpy matplotlib seaborn scikit-learn statsmodels scipy jupyter
```

## 运行方式

### 方式一：运行完整分析（推荐）

```bash
python 完整分析运行脚本.py
```

这将运行所有分析并生成论文所需的全部图表。

### 方式二：分步运行

1. 首先运行主要分析：
```bash
python 电力负荷预测分析.py
```

2. 然后运行能耗模式分析：
```bash
python 能耗模式分析.py
```

## 生成的图表

运行完成后，将在当前目录生成以下图表文件：

1. `图表3_电力负荷时间序列图.png` - 电力负荷时间序列图
2. `图表4_负荷数据季节性分解图.png` - 负荷数据的季节性分解图
3. `图表5_相关性热力图.png` - 负荷与气象因素的相关性热力图
4. `图表6_模型训练过程.png` - 模型训练过程的损失函数变化图
5. `图表7_模型预测结果对比.png` - 不同模型的预测结果对比图
6. `图表8_模型性能对比表.png` - 模型性能评估指标对比表
7. `图表9_能耗模式聚类结果.png` - 能耗模式聚类结果图
8. `图表10_季节性能耗模式对比.png` - 不同季节的能耗模式对比图
9. `图表11_特征重要性分析.png` - 特征重要性分析图

## 论文结构

论文包含以下主要部分：

1. **摘要** - 研究概述和主要结论
2. **引言** - 研究背景、意义和内容
3. **理论基础** - 数据仓库技术、时间序列分析、回归分析
4. **研究方法** - 数据来源、预处理、模型构建
5. **实验设计与实现** - 实验环境、数据分析、模型训练
6. **结果分析与讨论** - 预测结果、能耗模式、影响因素分析
7. **结论与展望** - 主要结论、研究贡献、未来展望
8. **参考文献** - 相关文献引用

## 技术特点

### 数据处理
- 生成模拟的电力负荷数据（包含季节性、周期性、随机性）
- 数据预处理（缺失值处理、异常值检测、特征工程）
- 时间序列特征提取

### 模型方法
- **时间序列模型**：ARIMA模型
- **回归模型**：线性回归、岭回归
- **机器学习模型**：随机森林
- **聚类分析**：K-means聚类

### 评估指标
- 平均绝对误差（MAE）
- 均方根误差（RMSE）
- 平均绝对百分比误差（MAPE）
- 决定系数（R²）

## 主要发现

1. **时间特征重要性**：小时、星期等时间特征对负荷预测具有重要影响
2. **气象因素影响**：温度是影响电力负荷的最重要气象因素
3. **季节性模式**：夏季和冬季负荷较高，春秋季节负荷相对较低
4. **工作日效应**：工作日负荷明显高于周末负荷

## 注意事项

1. 本项目使用模拟数据，实际应用时需要使用真实的电力负荷数据
2. 模型参数可能需要根据具体数据进行调优
3. 图表中的中文显示需要系统支持中文字体

## 作者信息

- 专业：数据科学与大数据技术
- 课程：《数据仓库技术与应用》
- 指导教师：李亚楠
- 完成时间：2024年

## 联系方式

如有问题，请联系作者或查看课程相关资料。
