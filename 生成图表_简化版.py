#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成论文图表 - 简化版（无显示，只保存）
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from statsmodels.tsa.seasonal import seasonal_decompose
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

def generate_data():
    """生成电力负荷数据"""
    print("正在生成电力负荷数据...")
    np.random.seed(42)
    
    n_samples = 2000  # 减少数据量以加快处理
    dates = pd.date_range('2023-01-01', periods=n_samples, freq='H')
    
    # 基础负荷模式
    base_load = 1000
    seasonal = 200 * np.sin(2 * np.pi * np.arange(n_samples) / (365.25 * 24))
    daily = 300 * np.sin(2 * np.pi * np.arange(n_samples) / 24 - np.pi/2)
    weekly = 150 * np.sin(2 * np.pi * np.arange(n_samples) / (7 * 24))
    
    # 工作日/周末差异
    is_weekend = pd.Series(dates).dt.dayofweek >= 5
    weekend_effect = np.where(is_weekend, -100, 50)
    
    # 气象数据
    temperature = 15 + 10 * np.sin(2 * np.pi * np.arange(n_samples) / (365.25 * 24)) + \
                 np.random.normal(0, 3, n_samples)
    humidity = 60 + 20 * np.sin(2 * np.pi * np.arange(n_samples) / (365.25 * 24)) + \
              np.random.normal(0, 10, n_samples)
    humidity = np.clip(humidity, 20, 95)
    wind_speed = 3 + np.random.exponential(2, n_samples)
    wind_speed = np.clip(wind_speed, 0, 15)
    precipitation = np.random.exponential(0.5, n_samples)
    
    # 温度影响
    temp_effect = np.where(temperature > 25, (temperature - 25) * 20,
                          np.where(temperature < 5, (5 - temperature) * 15, 0))
    
    # 噪声
    noise = np.random.normal(0, 50, n_samples)
    
    # 合成负荷
    load = base_load + seasonal + daily + weekly + weekend_effect + temp_effect + noise
    load = np.maximum(load, 100)
    
    # 创建数据框
    data = pd.DataFrame({
        'datetime': dates,
        'load': load,
        'temperature': temperature,
        'humidity': humidity,
        'wind_speed': wind_speed,
        'precipitation': precipitation,
        'hour': dates.hour,
        'day_of_week': dates.dayofweek,
        'month': dates.month,
        'is_weekend': is_weekend.astype(int)
    })
    
    data.set_index('datetime', inplace=True)
    print(f"数据生成完成，大小: {data.shape}")
    return data

def chart_1_statistics_table(data):
    """图表1：数据统计表"""
    print("生成图表1：数据统计表")
    
    stats = data[['load', 'temperature', 'humidity', 'wind_speed', 'precipitation']].describe()
    
    fig, ax = plt.subplots(figsize=(12, 8))
    ax.axis('tight')
    ax.axis('off')
    
    table_data = []
    for index in stats.index:
        row = [index] + [f"{val:.2f}" for val in stats.loc[index]]
        table_data.append(row)
    
    columns = ['统计指标', '负荷(MW)', '温度(°C)', '湿度(%)', '风速(m/s)', '降水量(mm)']
    
    table = ax.table(cellText=table_data, colLabels=columns, cellLoc='center', loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(12)
    table.scale(1.2, 2)
    
    for i in range(len(columns)):
        table[(0, i)].set_facecolor('#4CAF50')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    plt.title('数据集基本统计信息表', fontsize=16, pad=20, weight='bold')
    plt.tight_layout()
    plt.savefig('图表1_数据集基本统计信息表.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("图表1保存完成")

def chart_3_time_series(data):
    """图表3：时间序列图"""
    print("生成图表3：时间序列图")
    
    fig, axes = plt.subplots(3, 1, figsize=(16, 12))
    
    # 一周数据
    week_data = data.iloc[:24*7]
    axes[0].plot(week_data.index, week_data['load'], linewidth=1.5, color='blue')
    axes[0].set_title('电力负荷时间序列图 - 一周数据', fontsize=14, weight='bold')
    axes[0].set_ylabel('负荷 (MW)')
    axes[0].grid(True, alpha=0.3)
    
    # 一月数据
    month_data = data.iloc[:24*30]
    axes[1].plot(month_data.index, month_data['load'], linewidth=1, color='red')
    axes[1].set_title('电力负荷时间序列图 - 一月数据', fontsize=14, weight='bold')
    axes[1].set_ylabel('负荷 (MW)')
    axes[1].grid(True, alpha=0.3)
    
    # 月度平均
    monthly_avg = data.groupby(data.index.month)['load'].mean()
    axes[2].plot(monthly_avg.index, monthly_avg.values, 'o-', linewidth=2, markersize=8, color='green')
    axes[2].set_title('月度平均负荷变化', fontsize=14, weight='bold')
    axes[2].set_xlabel('月份')
    axes[2].set_ylabel('平均负荷 (MW)')
    axes[2].set_xticks(range(1, 13))
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('图表3_电力负荷时间序列图.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("图表3保存完成")

def chart_4_seasonal_decomposition(data):
    """图表4：季节性分解"""
    print("生成图表4：季节性分解图")
    
    # 使用前1个月数据
    decomp_data = data['load'].iloc[:24*30]
    decomposition = seasonal_decompose(decomp_data, model='additive', period=24)
    
    fig, axes = plt.subplots(4, 1, figsize=(16, 12))
    
    axes[0].plot(decomposition.observed, linewidth=1, color='black')
    axes[0].set_title('季节性分解 - 原始数据', fontsize=14, weight='bold')
    axes[0].set_ylabel('负荷 (MW)')
    axes[0].grid(True, alpha=0.3)
    
    axes[1].plot(decomposition.trend, linewidth=1.5, color='red')
    axes[1].set_title('趋势成分', fontsize=14, weight='bold')
    axes[1].set_ylabel('负荷 (MW)')
    axes[1].grid(True, alpha=0.3)
    
    axes[2].plot(decomposition.seasonal, linewidth=1, color='green')
    axes[2].set_title('季节性成分', fontsize=14, weight='bold')
    axes[2].set_ylabel('负荷 (MW)')
    axes[2].grid(True, alpha=0.3)
    
    axes[3].plot(decomposition.resid, linewidth=1, color='orange')
    axes[3].set_title('残差成分', fontsize=14, weight='bold')
    axes[3].set_ylabel('负荷 (MW)')
    axes[3].set_xlabel('时间')
    axes[3].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('图表4_负荷数据季节性分解图.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("图表4保存完成")

def chart_5_correlation_heatmap(data):
    """图表5：相关性热力图"""
    print("生成图表5：相关性热力图")
    
    correlation_cols = ['load', 'temperature', 'humidity', 'wind_speed', 'precipitation', 
                       'hour', 'day_of_week', 'month', 'is_weekend']
    correlation_matrix = data[correlation_cols].corr()
    
    plt.figure(figsize=(12, 10))
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
    sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='RdBu_r', center=0,
               square=True, linewidths=0.5, cbar_kws={"shrink": .8}, fmt='.3f')
    plt.title('负荷与各因素相关性热力图', fontsize=16, weight='bold')
    plt.tight_layout()
    plt.savefig('图表5_相关性热力图.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("图表5保存完成")

def main():
    """主函数"""
    print("=== 开始生成论文图表（简化版）===")
    
    try:
        # 生成数据
        data = generate_data()
        
        # 生成图表
        chart_1_statistics_table(data)
        chart_3_time_series(data)
        chart_4_seasonal_decomposition(data)
        chart_5_correlation_heatmap(data)
        
        print("\n=== 图表生成完成！===")
        print("已生成的图表文件：")
        print("- 图表1_数据集基本统计信息表.png")
        print("- 图表3_电力负荷时间序列图.png")
        print("- 图表4_负荷数据季节性分解图.png")
        print("- 图表5_相关性热力图.png")
        
        return data
        
    except Exception as e:
        print(f"生成图表时出错: {e}")
        return None

if __name__ == "__main__":
    data = main()
