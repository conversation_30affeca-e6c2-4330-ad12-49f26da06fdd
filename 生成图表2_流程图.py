#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成图表2：数据预处理流程图
"""

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

def create_flowchart():
    """创建数据预处理流程图"""
    print("生成图表2：数据预处理流程图")
    
    fig, ax = plt.subplots(figsize=(14, 10))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    # 定义颜色
    colors = {
        'start': '#4CAF50',
        'process': '#2196F3', 
        'decision': '#FF9800',
        'end': '#F44336'
    }
    
    # 流程步骤
    steps = [
        {'text': '原始电力负荷数据', 'pos': (5, 11), 'type': 'start'},
        {'text': '数据质量检查', 'pos': (5, 9.5), 'type': 'process'},
        {'text': '缺失值处理', 'pos': (2, 8), 'type': 'process'},
        {'text': '异常值检测', 'pos': (5, 8), 'type': 'process'},
        {'text': '数据标准化', 'pos': (8, 8), 'type': 'process'},
        {'text': '特征工程', 'pos': (5, 6.5), 'type': 'process'},
        {'text': '时间特征提取', 'pos': (2, 5), 'type': 'process'},
        {'text': '滞后特征构造', 'pos': (5, 5), 'type': 'process'},
        {'text': '移动平均特征', 'pos': (8, 5), 'type': 'process'},
        {'text': '数据验证', 'pos': (5, 3.5), 'type': 'decision'},
        {'text': '清洗后的数据集', 'pos': (5, 2), 'type': 'end'}
    ]
    
    # 绘制流程框
    boxes = []
    for step in steps:
        x, y = step['pos']
        color = colors[step['type']]
        
        if step['type'] == 'decision':
            # 菱形
            diamond = patches.RegularPolygon((x, y), 4, radius=0.8, 
                                           orientation=np.pi/4, 
                                           facecolor=color, 
                                           edgecolor='black', 
                                           alpha=0.8)
            ax.add_patch(diamond)
        else:
            # 矩形
            if step['type'] == 'start' or step['type'] == 'end':
                box = FancyBboxPatch((x-1, y-0.3), 2, 0.6, 
                                   boxstyle="round,pad=0.1",
                                   facecolor=color, 
                                   edgecolor='black',
                                   alpha=0.8)
            else:
                box = FancyBboxPatch((x-1, y-0.3), 2, 0.6, 
                                   boxstyle="square,pad=0.1",
                                   facecolor=color, 
                                   edgecolor='black',
                                   alpha=0.8)
            ax.add_patch(box)
        
        # 添加文本
        ax.text(x, y, step['text'], ha='center', va='center', 
               fontsize=10, fontweight='bold', color='white')
        
        boxes.append((x, y))
    
    # 绘制箭头连接
    arrows = [
        (0, 1),  # 原始数据 -> 数据质量检查
        (1, 2),  # 数据质量检查 -> 缺失值处理
        (1, 3),  # 数据质量检查 -> 异常值检测
        (1, 4),  # 数据质量检查 -> 数据标准化
        (2, 5),  # 缺失值处理 -> 特征工程
        (3, 5),  # 异常值检测 -> 特征工程
        (4, 5),  # 数据标准化 -> 特征工程
        (5, 6),  # 特征工程 -> 时间特征提取
        (5, 7),  # 特征工程 -> 滞后特征构造
        (5, 8),  # 特征工程 -> 移动平均特征
        (6, 9),  # 时间特征提取 -> 数据验证
        (7, 9),  # 滞后特征构造 -> 数据验证
        (8, 9),  # 移动平均特征 -> 数据验证
        (9, 10)  # 数据验证 -> 清洗后的数据集
    ]
    
    for start_idx, end_idx in arrows:
        start_pos = boxes[start_idx]
        end_pos = boxes[end_idx]
        
        # 计算箭头位置
        dx = end_pos[0] - start_pos[0]
        dy = end_pos[1] - start_pos[1]
        
        # 调整起点和终点位置，避免与框重叠
        if abs(dx) > abs(dy):  # 水平方向
            if dx > 0:
                start_x, start_y = start_pos[0] + 1, start_pos[1]
                end_x, end_y = end_pos[0] - 1, end_pos[1]
            else:
                start_x, start_y = start_pos[0] - 1, start_pos[1]
                end_x, end_y = end_pos[0] + 1, end_pos[1]
        else:  # 垂直方向
            if dy > 0:
                start_x, start_y = start_pos[0], start_pos[1] + 0.3
                end_x, end_y = end_pos[0], end_pos[1] - 0.3
            else:
                start_x, start_y = start_pos[0], start_pos[1] - 0.3
                end_x, end_y = end_pos[0], end_pos[1] + 0.3
        
        ax.annotate('', xy=(end_x, end_y), xytext=(start_x, start_y),
                   arrowprops=dict(arrowstyle='->', lw=2, color='black'))
    
    # 添加说明文本
    explanation_text = [
        "数据预处理流程说明：",
        "1. 缺失值处理：时间序列插值、均值填充",
        "2. 异常值检测：3σ准则、Isolation Forest",
        "3. 数据标准化：Z-score标准化、Min-Max标准化",
        "4. 特征工程：时间特征、滞后特征、移动平均"
    ]
    
    for i, text in enumerate(explanation_text):
        ax.text(0.5, 1.2 - i*0.2, text, fontsize=9, 
               fontweight='bold' if i == 0 else 'normal',
               transform=ax.transAxes)
    
    plt.title('数据预处理流程图', fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.savefig('图表2_数据预处理流程图.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("图表2保存完成")

def main():
    """主函数"""
    print("=== 生成数据预处理流程图 ===")
    create_flowchart()
    print("=== 流程图生成完成！===")

if __name__ == "__main__":
    main()
