#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
电力系统负荷预测与能耗分析 - 测试版本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def test_basic_functionality():
    """测试基本功能"""
    print("=== 电力系统负荷预测与能耗分析 - 测试版 ===")
    
    # 生成简单的模拟数据
    np.random.seed(42)
    n_samples = 100
    dates = pd.date_range('2023-01-01', periods=n_samples, freq='H')
    
    # 基础负荷模式
    base_load = 1000
    daily = 300 * np.sin(2 * np.pi * np.arange(n_samples) / 24)
    noise = np.random.normal(0, 50, n_samples)
    load = base_load + daily + noise
    
    # 创建数据框
    data = pd.DataFrame({
        'datetime': dates,
        'load': load,
        'temperature': 15 + np.random.normal(0, 3, n_samples),
        'hour': dates.hour
    })
    
    print(f"数据集生成成功，大小: {data.shape}")
    print("数据集前5行:")
    print(data.head())
    
    # 基本统计
    print("\n基本统计信息:")
    print(data['load'].describe())
    
    print("\n=== 测试完成！代码可以正常运行 ===")
    return True

if __name__ == "__main__":
    test_basic_functionality()
