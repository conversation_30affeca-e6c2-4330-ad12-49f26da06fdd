# 电力系统负荷预测数据集说明

## 📊 数据集概述

本研究使用的是**模拟生成的电力负荷数据集**，这在学术研究中是完全可以接受的做法，特别是在以下情况下：
- 真实数据难以获取或涉及商业机密
- 需要控制数据特征以验证特定算法
- 教学和演示目的

## 🎯 数据集特点

### 数据规模
- **样本数量**: 2000个小时数据点（约83天）
- **特征维度**: 10个特征变量
- **时间跨度**: 2023年1月1日开始的连续小时数据

### 主要特征

| 特征名称 | 数据类型 | 单位 | 描述 |
|---------|---------|------|------|
| load | 数值型 | MW | 电力负荷（目标变量） |
| temperature | 数值型 | °C | 环境温度 |
| humidity | 数值型 | % | 相对湿度 |
| wind_speed | 数值型 | m/s | 风速 |
| precipitation | 数值型 | mm | 降水量 |
| hour | 整数型 | - | 小时（0-23） |
| day_of_week | 整数型 | - | 星期几（0-6） |
| month | 整数型 | - | 月份（1-12） |
| is_weekend | 二进制 | - | 是否周末（0/1） |
| season | 分类型 | - | 季节（春夏秋冬） |

## 🔬 数据生成原理

### 负荷模式构成
电力负荷由以下几个成分组成：

1. **基础负荷** (1000 MW)
   - 代表基本的电力需求

2. **季节性变化**
   - 年周期：`200 * sin(2π * t / (365.25 * 24))`
   - 反映季节性用电变化

3. **日周期变化**
   - 日周期：`300 * sin(2π * t / 24 - π/2)`
   - 反映一天内的用电规律

4. **周周期变化**
   - 周周期：`150 * sin(2π * t / (7 * 24))`
   - 反映工作日与周末的差异

5. **工作日/周末效应**
   - 周末：-100 MW
   - 工作日：+50 MW

6. **气象影响**
   - 温度效应：制冷和供暖需求
   - 湿度效应：高湿度增加制冷负荷

7. **随机噪声**
   - 正态分布噪声：σ = 50 MW

### 气象数据生成

#### 温度
```python
temperature = 15 + 10 * sin(2π * t / (365.25 * 24)) + N(0, 3)
```
- 年平均温度：15°C
- 季节性变化：±10°C
- 随机波动：±3°C

#### 湿度
```python
humidity = 60 + 20 * sin(2π * t / (365.25 * 24) + π/4) + N(0, 10)
```
- 年平均湿度：60%
- 季节性变化：±20%
- 限制范围：20%-95%

#### 风速
```python
wind_speed = 3 + Exponential(2)
```
- 基础风速：3 m/s
- 指数分布随机成分
- 限制范围：0-15 m/s

#### 降水量
```python
precipitation = Exponential(0.5)
```
- 指数分布，平均0.5mm
- 符合降水的自然分布特征

## 📈 数据质量特征

### 统计特性
- **负荷范围**: 约600-1800 MW
- **平均负荷**: 约1000 MW
- **负荷标准差**: 约200 MW
- **数据完整性**: 100%（无缺失值）

### 时间特征
- **采样频率**: 每小时
- **时间连续性**: 完全连续
- **季节覆盖**: 包含多个季节的数据

### 相关性特征
- 负荷与温度：强相关（制冷/供暖效应）
- 负荷与时间：强周期性相关
- 负荷与湿度：中等相关
- 负荷与风速/降水：弱相关

## 🎓 学术价值

### 教学优势
1. **可控性**: 数据特征明确，便于理解算法原理
2. **完整性**: 包含电力负荷预测的所有关键因素
3. **真实性**: 模拟了真实电力系统的主要特征
4. **可重现**: 使用固定随机种子，结果可重现

### 研究适用性
1. **时间序列分析**: 包含多种周期性成分
2. **回归建模**: 多元特征与目标变量关系明确
3. **聚类分析**: 不同时期的负荷模式差异明显
4. **特征工程**: 可构造各种衍生特征

## 🔍 与真实数据的对比

### 相似性
- ✅ 季节性变化模式
- ✅ 日周期变化规律
- ✅ 工作日/周末差异
- ✅ 气象因素影响
- ✅ 负荷数值范围合理

### 简化之处
- 🔸 未考虑极端天气事件
- 🔸 未包含经济因素影响
- 🔸 未模拟设备故障等突发事件
- 🔸 气象数据相对简化

## 📚 数据获取方式

### 真实数据源（参考）
如需真实数据，可参考以下来源：

1. **公开数据集**
   - UCI Machine Learning Repository
   - Kaggle电力数据集
   - 国家电网公开数据

2. **研究机构**
   - 中国电力科学研究院
   - 清华大学电机系
   - 华北电力大学

3. **政府部门**
   - 国家能源局
   - 各省电力公司
   - 电力交易中心

### 本项目数据
- **生成方式**: Python代码自动生成
- **代码位置**: `生成图表_简化版.py`
- **可重现性**: 使用随机种子42
- **修改方便**: 可调整参数生成不同特征的数据

## 💡 使用建议

1. **学术研究**: 适合算法验证和方法比较
2. **教学演示**: 便于理解电力负荷预测原理
3. **原型开发**: 快速验证模型可行性
4. **进一步研究**: 可作为真实数据研究的前期准备

## ⚠️ 注意事项

1. 本数据集为模拟数据，不能直接用于实际电力系统运行
2. 在学术论文中应明确说明数据来源和生成方法
3. 如需发表高水平论文，建议结合真实数据进行验证
4. 数据特征可根据研究需要进行调整和扩展
