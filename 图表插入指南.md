# 📊 论文图表插入指南

## 🎯 图表对应关系

以下是论文中标注的图表位置与生成的图片文件的对应关系：

### 📋 图表清单

| 论文标注 | 图片文件 | 插入位置 | 说明 |
|---------|---------|---------|------|
| **[图表标注1]** | `图表1_数据集基本统计信息表.png` | 3.1 数据来源与描述 | 数据集基本信息统计表 |
| **[图表标注2]** | `图表2_数据预处理流程图.png` | 3.2 数据预处理 | 数据预处理流程图 |
| **[图表标注3]** | `图表3_电力负荷时间序列图.png` | 4.2 数据探索性分析 | 电力负荷时间序列图 |
| **[图表标注4]** | `图表4_负荷数据季节性分解图.png` | 4.2 数据探索性分析 | 负荷数据的季节性分解图 |
| **[图表标注5]** | `图表5_相关性热力图.png` | 4.2 数据探索性分析 | 负荷与气象因素的相关性热力图 |
| **[图表标注6]** | `图表6_模型训练过程.png` | 4.3 模型训练与验证 | 模型训练过程的损失函数变化图 |
| **[图表标注7]** | `图表7_模型预测结果对比.png` | 5.1 预测结果分析 | 不同模型的预测结果对比图 |
| **[图表标注8]** | `图表8_模型性能对比表.png` | 5.1 预测结果分析 | 模型性能评估指标对比表 |
| **[图表标注9]** | `图表9_能耗模式聚类结果.png` | 5.2 能耗模式分析 | 能耗模式聚类结果图 |
| **[图表标注10]** | `图表10_季节性能耗模式对比.png` | 5.2 能耗模式分析 | 不同季节的能耗模式对比图 |
| **[图表标注11]** | `图表11_特征重要性分析.png` | 5.3 影响因素分析 | 特征重要性分析图 |

## 📝 插入步骤

### 1. 在Word中插入图片
1. 将光标定位到论文中的 **[图表标注X]** 位置
2. 删除标注文字
3. 点击 **插入** → **图片** → **来自文件**
4. 选择对应的PNG文件
5. 调整图片大小和位置

### 2. 设置图片格式
- **大小**: 建议宽度为14-16cm
- **对齐**: 居中对齐
- **环绕**: 上下型环绕
- **分辨率**: 已设置为300DPI，保证清晰度

### 3. 添加图题
在每个图片下方添加图题，格式如下：

```
图X.X 图表标题
```

例如：
```
图3.1 数据集基本统计信息表
图4.1 电力负荷时间序列图
图4.2 负荷数据的季节性分解图
```

## 🎨 图表特点说明

### 图表1：数据集基本统计信息表
- **类型**: 统计表格
- **内容**: 负荷、温度、湿度、风速、降水量的描述性统计
- **用途**: 展示数据集的基本特征

### 图表2：数据预处理流程图
- **类型**: 流程图
- **内容**: 从原始数据到清洗数据的完整流程
- **用途**: 说明数据预处理的系统性方法

### 图表3：电力负荷时间序列图
- **类型**: 时间序列图
- **内容**: 一周、一月和月度平均负荷变化
- **用途**: 展示负荷的时间变化特征

### 图表4：负荷数据季节性分解图
- **类型**: 分解图
- **内容**: 原始数据、趋势、季节性、残差四个成分
- **用途**: 分析负荷的周期性成分

### 图表5：相关性热力图
- **类型**: 热力图
- **内容**: 各变量间的相关系数矩阵
- **用途**: 识别变量间的相关关系

### 图表6：模型训练过程图
- **类型**: 线图
- **内容**: 训练损失和验证损失的变化
- **用途**: 展示模型训练的收敛过程

### 图表7：模型预测结果对比图
- **类型**: 组合图
- **内容**: 预测值vs实际值、误差分析、散点图
- **用途**: 比较不同模型的预测性能

### 图表8：模型性能对比表
- **类型**: 性能表格
- **内容**: MAE、RMSE、MAPE、R²等指标
- **用途**: 量化比较模型性能

### 图表9：能耗模式聚类结果图
- **类型**: 聚类图
- **内容**: PCA降维、特征散点图、聚类分布
- **用途**: 识别不同的能耗模式

### 图表10：季节性能耗模式对比图
- **类型**: 组合图
- **内容**: 季节性负荷分布、日负荷曲线、温度关系、月度统计
- **用途**: 分析季节性用电特征

### 图表11：特征重要性分析图
- **类型**: 条形图+饼图
- **内容**: 各特征对负荷预测的重要性排序
- **用途**: 识别关键影响因素

## ✅ 质量检查

### 图片质量
- ✅ 分辨率：300DPI，适合打印
- ✅ 格式：PNG，支持透明背景
- ✅ 大小：文件大小适中，加载快速
- ✅ 清晰度：文字和线条清晰可读

### 内容完整性
- ✅ 标题：每个图表都有清晰的标题
- ✅ 坐标轴：标签和单位明确
- ✅ 图例：颜色和符号说明完整
- ✅ 中文：支持中文显示，无乱码

### 学术规范
- ✅ 编号：按照论文结构顺序编号
- ✅ 引用：在正文中有对应的引用
- ✅ 说明：每个图表都有详细的文字说明
- ✅ 来源：数据来源和处理方法明确

## 🔧 常见问题解决

### 1. 图片显示模糊
- **原因**: Word压缩了图片
- **解决**: 右键图片 → 设置图片格式 → 图片 → 取消"压缩图片"

### 2. 中文显示异常
- **原因**: 字体不支持
- **解决**: 图片已使用SimHei字体，应该正常显示

### 3. 图片大小不合适
- **原因**: 默认插入大小
- **解决**: 右键图片 → 设置图片格式 → 大小 → 调整宽度为14-16cm

### 4. 图表编号混乱
- **原因**: 插入顺序问题
- **解决**: 按照论文中的标注顺序依次插入

## 📋 最终检查清单

插入完成后，请检查：

- [ ] 所有11个图表都已插入
- [ ] 图表位置与论文标注一致
- [ ] 图片清晰，文字可读
- [ ] 图题格式统一
- [ ] 图表编号连续
- [ ] 正文中有对应的图表引用
- [ ] 图表大小适中，版面美观

## 💡 提示

1. **保存备份**: 插入图片前先保存论文备份
2. **分批插入**: 建议分批插入，避免文档过大
3. **格式统一**: 保持所有图表的格式一致
4. **文字说明**: 每个图表后都要有详细的文字分析
5. **交叉引用**: 使用Word的交叉引用功能，便于后续修改
